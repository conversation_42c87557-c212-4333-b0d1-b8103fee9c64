// JWT Token Debugger Utility
export const decodeJWT = (token) => {
  try {
    if (!token) {
      return { error: 'No token provided' };
    }

    // Remove 'Bearer ' prefix if present
    const cleanToken = token.startsWith('Bearer ') ? token.slice(7) : token;
    
    // Split the token into parts
    const parts = cleanToken.split('.');
    if (parts.length !== 3) {
      return { error: 'Invalid token format - should have 3 parts' };
    }

    // Decode header
    const header = JSON.parse(atob(parts[0]));
    
    // Decode payload
    const payload = JSON.parse(atob(parts[1]));
    
    // Check expiration
    const now = Math.floor(Date.now() / 1000);
    const isExpired = payload.exp && payload.exp < now;
    const timeUntilExpiry = payload.exp ? payload.exp - now : null;
    
    return {
      header,
      payload,
      isExpired,
      timeUntilExpiry,
      expiryDate: payload.exp ? new Date(payload.exp * 1000) : null,
      issuedAt: payload.iat ? new Date(payload.iat * 1000) : null,
      valid: !isExpired
    };
  } catch (error) {
    return { error: `Failed to decode token: ${error.message}` };
  }
};

export const debugToken = () => {
  const token = localStorage.getItem('token');
  const decoded = decodeJWT(token);
  
  console.log('🔍 JWT Token Debug:', {
    tokenExists: !!token,
    tokenLength: token?.length,
    tokenStart: token?.substring(0, 20) + '...',
    decoded
  });
  
  return decoded;
};
