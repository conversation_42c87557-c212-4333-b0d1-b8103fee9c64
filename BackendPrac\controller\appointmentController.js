const Appointment = require('../model/appointmentModel');
const User = require('../model/rawDataModel');
const mongoose = require('mongoose');

// Create new appointment
exports.createAppointment = async (req, res) => {
    try {
        console.log('📅 Creating new appointment...');
        console.log('📅 Request body:', req.body);
        console.log('📅 User from token:', req.user);

        const {
            // Customer Information
            customerName,
            email,
            phone,
            address,
            emergencyContact,

            // Pet Information
            petName,
            petType,
            breed,
            age,
            weight,
            gender,

            // Appointment Details
            appointmentDate,
            appointmentTime,
            serviceType,
            preferredVet,

            // Additional Information
            symptoms,
            previousVisits,
            medications,
            specialRequirements,
            notes
        } = req.body;

        // Validate required fields
        const requiredFields = {
            customerName,
            email,
            phone,
            address,
            petName,
            petType,
            age,
            gender,
            appointmentDate,
            appointmentTime,
            serviceType
        };

        const missingFields = Object.entries(requiredFields)
            .filter(([key, value]) => !value || (typeof value === 'string' && !value.trim()))
            .map(([key]) => key);

        if (missingFields.length > 0) {
            console.log('❌ Missing required fields:', missingFields);
            return res.status(400).json({
                success: false,
                message: 'Missing required fields',
                missingFields
            });
        }

        // Validate appointment date is in the future
        const selectedDate = new Date(appointmentDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            return res.status(400).json({
                success: false,
                message: 'Appointment date must be in the future'
            });
        }

        // Check for appointment conflicts (same date and time)
        const existingAppointment = await Appointment.findOne({
            appointmentDate: selectedDate,
            appointmentTime,
            status: { $nin: ['cancelled'] }
        });

        if (existingAppointment) {
            return res.status(409).json({
                success: false,
                message: 'This time slot is already booked. Please choose a different time.',
                conflictingAppointment: {
                    appointmentNumber: existingAppointment.appointmentNumber,
                    petName: existingAppointment.petName
                }
            });
        }

        // Create new appointment
        const appointmentData = {
            user: req.user.id,
            customerName: customerName.trim(),
            email: email.trim().toLowerCase(),
            phone: phone.trim(),
            address: address.trim(),
            emergencyContact: emergencyContact?.trim(),

            petName: petName.trim(),
            petType,
            breed: breed?.trim(),
            age: age.trim(),
            weight: weight?.trim(),
            gender,

            appointmentDate: selectedDate,
            appointmentTime,
            serviceType,
            preferredVet: preferredVet || '',

            symptoms: symptoms?.trim(),
            previousVisits: previousVisits?.trim(),
            medications: medications?.trim(),
            specialRequirements: specialRequirements?.trim(),
            notes: notes?.trim(),

            status: 'pending',
            priority: symptoms?.toLowerCase().includes('emergency') ? 'urgent' : 'normal'
        };

        console.log('📅 Creating appointment with data:', JSON.stringify(appointmentData, null, 2));
        const newAppointment = await Appointment.create(appointmentData);
        console.log('✅ Appointment created successfully:', newAppointment.appointmentNumber);

        // Populate user information for response
        const populatedAppointment = await Appointment.findById(newAppointment._id)
            .populate('user', 'name email');

        res.status(201).json({
            success: true,
            message: 'Appointment booked successfully',
            appointment: populatedAppointment
        });

    } catch (error) {
        console.error('❌ Error creating appointment:', error);

        if (error.name === 'ValidationError') {
            const validationErrors = Object.values(error.errors).map(err => err.message);
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: validationErrors
            });
        }

        if (error.code === 11000) {
            return res.status(409).json({
                success: false,
                message: 'Appointment number conflict. Please try again.'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Failed to create appointment',
            error: error.message
        });
    }
};

// Get all appointments (admin)
exports.getAllAppointments = async (req, res) => {
    try {
        console.log('📅 Getting all appointments...');

        const {
            page = 1,
            limit = 20,
            status,
            search,
            date,
            serviceType,
            petType
        } = req.query;

        // Build filter object
        let filter = {};

        if (status && status !== 'all') {
            filter.status = status;
        }

        if (date) {
            const selectedDate = new Date(date);
            const nextDay = new Date(selectedDate);
            nextDay.setDate(nextDay.getDate() + 1);

            filter.appointmentDate = {
                $gte: selectedDate,
                $lt: nextDay
            };
        }

        if (serviceType && serviceType !== 'all') {
            filter.serviceType = serviceType;
        }

        if (petType && petType !== 'all') {
            filter.petType = petType;
        }

        if (search) {
            filter.$or = [
                { customerName: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { petName: { $regex: search, $options: 'i' } },
                { appointmentNumber: { $regex: search, $options: 'i' } },
                { phone: { $regex: search, $options: 'i' } }
            ];
        }

        console.log('📅 Filter applied:', filter);

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [appointments, totalCount] = await Promise.all([
            Appointment.find(filter)
                .populate('user', 'name email')
                .sort({ appointmentDate: 1, appointmentTime: 1 })
                .skip(skip)
                .limit(parseInt(limit)),
            Appointment.countDocuments(filter)
        ]);

        console.log(`✅ Found ${appointments.length} appointments out of ${totalCount} total`);

        res.status(200).json({
            success: true,
            appointments,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalCount / parseInt(limit)),
                totalCount,
                hasNext: skip + appointments.length < totalCount,
                hasPrev: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('❌ Error fetching appointments:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch appointments',
            error: error.message
        });
    }
};

// Get user's appointments
exports.getUserAppointments = async (req, res) => {
    try {
        console.log('📅 Getting user appointments for user:', req.user.id);

        const {
            page = 1,
            limit = 10,
            status
        } = req.query;

        let filter = { user: req.user.id };

        if (status && status !== 'all') {
            filter.status = status;
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [appointments, totalCount] = await Promise.all([
            Appointment.find(filter)
                .sort({ appointmentDate: -1, appointmentTime: -1 })
                .skip(skip)
                .limit(parseInt(limit)),
            Appointment.countDocuments(filter)
        ]);

        console.log(`✅ Found ${appointments.length} appointments for user`);

        res.status(200).json({
            success: true,
            appointments,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalCount / parseInt(limit)),
                totalCount,
                hasNext: skip + appointments.length < totalCount,
                hasPrev: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('❌ Error fetching user appointments:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch appointments',
            error: error.message
        });
    }
};

// Get single appointment by ID
exports.getAppointmentById = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        console.log('📅 Getting appointment by ID:', appointmentId);

        if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid appointment ID format'
            });
        }

        const appointment = await Appointment.findById(appointmentId)
            .populate('user', 'name email');

        if (!appointment) {
            return res.status(404).json({
                success: false,
                message: 'Appointment not found'
            });
        }

        console.log('✅ Appointment found:', appointment.appointmentNumber);

        res.status(200).json({
            success: true,
            appointment
        });

    } catch (error) {
        console.error('❌ Error getting appointment:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve appointment',
            error: error.message
        });
    }
};

// Update appointment status (admin)
exports.updateAppointmentStatus = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { status, adminNotes, assignedVet, cancellationReason } = req.body;

        console.log('📅 Updating appointment status:', { appointmentId, status, adminNotes });

        if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid appointment ID format'
            });
        }

        const appointment = await Appointment.findById(appointmentId);
        if (!appointment) {
            return res.status(404).json({
                success: false,
                message: 'Appointment not found'
            });
        }

        // Update fields
        const updateData = {
            status,
            lastUpdatedBy: req.user?.email || 'admin'
        };

        if (adminNotes) updateData.adminNotes = adminNotes;
        if (assignedVet) updateData.assignedVet = assignedVet;
        if (cancellationReason) updateData.cancellationReason = cancellationReason;

        // Set timestamp based on status
        if (status === 'confirmed') {
            updateData.confirmedAt = new Date();
        } else if (status === 'completed') {
            updateData.completedAt = new Date();
        } else if (status === 'cancelled') {
            updateData.cancelledAt = new Date();
        }

        const updatedAppointment = await Appointment.findByIdAndUpdate(
            appointmentId,
            updateData,
            { new: true, runValidators: true }
        ).populate('user', 'name email');

        console.log('✅ Appointment status updated:', updatedAppointment.appointmentNumber);

        res.status(200).json({
            success: true,
            message: 'Appointment status updated successfully',
            appointment: updatedAppointment
        });

    } catch (error) {
        console.error('❌ Error updating appointment status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update appointment status',
            error: error.message
        });
    }
};

// Reschedule appointment
exports.rescheduleAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { newDate, newTime, reason } = req.body;

        console.log('📅 Rescheduling appointment:', { appointmentId, newDate, newTime });

        if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid appointment ID format'
            });
        }

        const appointment = await Appointment.findById(appointmentId);
        if (!appointment) {
            return res.status(404).json({
                success: false,
                message: 'Appointment not found'
            });
        }

        // Validate new date is in the future
        const selectedDate = new Date(newDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            return res.status(400).json({
                success: false,
                message: 'New appointment date must be in the future'
            });
        }

        // Check for conflicts
        const existingAppointment = await Appointment.findOne({
            _id: { $ne: appointmentId },
            appointmentDate: selectedDate,
            appointmentTime: newTime,
            status: { $nin: ['cancelled'] }
        });

        if (existingAppointment) {
            return res.status(409).json({
                success: false,
                message: 'This time slot is already booked. Please choose a different time.'
            });
        }

        // Store original appointment details
        const originalDate = appointment.appointmentDate;
        const originalTime = appointment.appointmentTime;

        // Update appointment
        const updatedAppointment = await Appointment.findByIdAndUpdate(
            appointmentId,
            {
                appointmentDate: selectedDate,
                appointmentTime: newTime,
                status: 'rescheduled',
                rescheduledFrom: {
                    originalDate,
                    originalTime,
                    reason: reason || 'Rescheduled by admin'
                },
                lastUpdatedBy: req.user?.email || 'admin'
            },
            { new: true, runValidators: true }
        ).populate('user', 'name email');

        console.log('✅ Appointment rescheduled:', updatedAppointment.appointmentNumber);

        res.status(200).json({
            success: true,
            message: 'Appointment rescheduled successfully',
            appointment: updatedAppointment
        });

    } catch (error) {
        console.error('❌ Error rescheduling appointment:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reschedule appointment',
            error: error.message
        });
    }
};

// Delete appointment (admin only)
exports.deleteAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        console.log('📅 Deleting appointment:', appointmentId);

        if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid appointment ID format'
            });
        }

        const appointment = await Appointment.findById(appointmentId);
        if (!appointment) {
            return res.status(404).json({
                success: false,
                message: 'Appointment not found'
            });
        }

        await Appointment.findByIdAndDelete(appointmentId);
        console.log('✅ Appointment deleted:', appointment.appointmentNumber);

        res.status(200).json({
            success: true,
            message: 'Appointment deleted successfully'
        });

    } catch (error) {
        console.error('❌ Error deleting appointment:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete appointment',
            error: error.message
        });
    }
};

// Get appointment statistics (admin)
exports.getAppointmentStats = async (req, res) => {
    try {
        console.log('📅 Getting appointment statistics...');

        const stats = await Appointment.getStats();

        // Get additional statistics
        const totalAppointments = await Appointment.countDocuments();
        const pendingAppointments = await Appointment.countDocuments({ status: 'pending' });
        const confirmedAppointments = await Appointment.countDocuments({ status: 'confirmed' });
        const completedAppointments = await Appointment.countDocuments({ status: 'completed' });

        // Get upcoming appointments (next 7 days)
        const today = new Date();
        const nextWeek = new Date();
        nextWeek.setDate(today.getDate() + 7);

        const upcomingAppointments = await Appointment.find({
            appointmentDate: {
                $gte: today,
                $lte: nextWeek
            },
            status: { $in: ['pending', 'confirmed'] }
        }).sort({ appointmentDate: 1, appointmentTime: 1 }).limit(5);

        // Get appointments by service type
        const appointmentsByService = await Appointment.aggregate([
            {
                $group: {
                    _id: '$serviceType',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } }
        ]);

        res.status(200).json({
            success: true,
            stats: {
                total: totalAppointments,
                pending: pendingAppointments,
                confirmed: confirmedAppointments,
                completed: completedAppointments,
                todayCount: stats.todayCount,
                byStatus: stats.byStatus,
                byService: appointmentsByService,
                upcoming: upcomingAppointments
            }
        });

    } catch (error) {
        console.error('❌ Error fetching appointment stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch appointment statistics',
            error: error.message
        });
    }
};
