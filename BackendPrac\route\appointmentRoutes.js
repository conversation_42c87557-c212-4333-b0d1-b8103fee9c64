const express = require('express');
const router = express.Router();
const { AuthMiddleware } = require('../Middleware/AuthMiddleware');
const {
    createAppointment,
    getAllAppointments,
    getUserAppointments,
    getAppointmentById,
    updateAppointmentStatus,
    rescheduleAppointment,
    deleteAppointment,
    getAppointmentStats
} = require('../controller/appointmentController');

// Public routes (none for appointments - all require authentication)

// Protected routes (require authentication)
router.use(AuthMiddleware); // Apply authentication middleware to all routes below

// User routes
router.post('/create', createAppointment);
router.get('/my-appointments', getUserAppointments);
router.get('/:appointmentId', getAppointmentById);

// Admin routes (additional middleware could be added for admin-only access)
router.get('/', getAllAppointments); // Get all appointments (admin)
router.put('/:appointmentId/status', updateAppointmentStatus); // Update appointment status
router.put('/:appointmentId/reschedule', rescheduleAppointment); // Reschedule appointment
router.delete('/:appointmentId', deleteAppointment); // Delete appointment
router.get('/admin/stats', getAppointmentStats); // Get appointment statistics

module.exports = router;
