import React, { useState, useEffect } from 'react';
import Admin from './Admin';

const AdminWrapper = () => {
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simple initialization check
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading Admin Dashboard...</span>
          </div>
        </div>
      </div>
    );
  }

  // Render the Admin component
  return <Admin />;
};

export default AdminWrapper;
