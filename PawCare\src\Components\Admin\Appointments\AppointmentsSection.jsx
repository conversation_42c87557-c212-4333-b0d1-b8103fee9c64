import React, { useState, useEffect } from 'react';
import {
  FaSearch,
  FaEye,
  FaEdit,
  FaTrash,
  FaFilter,
  FaDownload,
  FaCalendarAlt,
  FaClock,
  FaPaw,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaStethoscope,
  FaCheck,
  FaTimes,
  FaExclamationTriangle
} from 'react-icons/fa';
import { appointmentService } from '../../../services/appointmentService';
import { debugAuthState, isAuthenticated } from '../../../utils/authUtils';

const AppointmentsSection = () => {
  const [appointments, setAppointments] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [serviceFilter, setServiceFilter] = useState('all');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [notification, setNotification] = useState({ type: '', message: '' });
  const [stats, setStats] = useState(null);

  useEffect(() => {
    fetchAppointments();
    fetchStats();
  }, []);

  // Refetch appointments when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchAppointments();
    }, 500); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, statusFilter, serviceFilter]);

  const fetchAppointments = async () => {
    setLoading(true);
    setError(null);

    try {
      // Check authentication first
      if (!isAuthenticated()) {
        throw new Error('Not authenticated - please log in');
      }

      debugAuthState();

      // Test authentication first
      console.log('🧪 Testing authentication before fetching appointments...');
      try {
        await appointmentService.testAuth();
        console.log('✅ Authentication test passed, proceeding with appointment fetch...');
      } catch (authError) {
        console.error('❌ Authentication test failed:', authError);
        throw new Error('Authentication test failed - please log in again');
      }

      const params = {
        page: 1,
        limit: 50,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        serviceType: serviceFilter !== 'all' ? serviceFilter : undefined,
        search: searchTerm || undefined
      };

      console.log('📅 Fetching appointments with params:', params);
      const response = await appointmentService.getAllAppointments(params);

      if (response.success) {
        setAppointments(response.appointments || []);
        console.log(`✅ Fetched ${response.appointments?.length || 0} appointments`);
      } else {
        throw new Error(response.message || 'Failed to fetch appointments');
      }

    } catch (error) {
      console.error('❌ Error fetching appointments:', error);
      setError(error.message || 'Failed to fetch appointments');
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await appointmentService.getAppointmentStats();
      if (response.success) {
        setStats(response.stats);
      }
    } catch (error) {
      console.error('❌ Error fetching appointment stats:', error);
    }
  };

  const handleStatusUpdate = async (appointmentId, newStatus, adminNotes = '') => {
    try {
      const response = await appointmentService.updateAppointmentStatus(appointmentId, {
        status: newStatus,
        adminNotes
      });

      if (response.success) {
        setNotification({ type: 'success', message: 'Appointment status updated successfully!' });
        fetchAppointments();
        fetchStats();
        setSelectedAppointment(null);
      } else {
        throw new Error(response.message || 'Failed to update appointment status');
      }
    } catch (error) {
      console.error('❌ Error updating appointment status:', error);
      setNotification({ type: 'error', message: error.message || 'Failed to update appointment status' });
    }

    // Clear notification after 3 seconds
    setTimeout(() => setNotification({ type: '', message: '' }), 3000);
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-blue-100 text-blue-800',
      'in-progress': 'bg-purple-100 text-purple-800',
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
      rescheduled: 'bg-orange-100 text-orange-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: 'text-green-600',
      normal: 'text-blue-600',
      high: 'text-orange-600',
      urgent: 'text-red-600'
    };
    return colors[priority] || 'text-gray-600';
  };

  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = !searchTerm ||
      appointment.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.petName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.appointmentNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.phone?.includes(searchTerm);

    const matchesStatus = statusFilter === 'all' || appointment.status === statusFilter;
    const matchesService = serviceFilter === 'all' || appointment.serviceType === serviceFilter;

    return matchesSearch && matchesStatus && matchesService;
  });

  return (
    <div className="p-6">
      {/* Notification */}
      {notification.message && (
        <div className={`mb-4 p-4 rounded-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-800">Appointment Management</h2>
          <p className="text-gray-600 mt-1">Manage and track all pet appointments</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-[#575CEE] text-white px-4 py-2 rounded-lg hover:bg-[#4a4fd1] transition-colors flex items-center">
            <FaDownload className="mr-2" />
            Export Appointments
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="bg-blue-500 text-white p-3 rounded-lg">
                <FaCalendarAlt />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">Total Appointments</p>
                <h3 className="text-xl font-semibold">{stats.total}</h3>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="bg-yellow-500 text-white p-3 rounded-lg">
                <FaClock />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">Pending</p>
                <h3 className="text-xl font-semibold">{stats.pending}</h3>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="bg-green-500 text-white p-3 rounded-lg">
                <FaCheck />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">Confirmed</p>
                <h3 className="text-xl font-semibold">{stats.confirmed}</h3>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="bg-purple-500 text-white p-3 rounded-lg">
                <FaStethoscope />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">Today</p>
                <h3 className="text-xl font-semibold">{stats.todayCount}</h3>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search appointments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="rescheduled">Rescheduled</option>
          </select>

          <select
            value={serviceFilter}
            onChange={(e) => setServiceFilter(e.target.value)}
            className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
          >
            <option value="all">All Services</option>
            <option value="General Checkup">General Checkup</option>
            <option value="Vaccination">Vaccination</option>
            <option value="Emergency Care">Emergency Care</option>
            <option value="Dental Care">Dental Care</option>
            <option value="Surgery Consultation">Surgery Consultation</option>
            <option value="Grooming">Grooming</option>
            <option value="Behavioral Consultation">Behavioral Consultation</option>
            <option value="Nutrition Consultation">Nutrition Consultation</option>
            <option value="Senior Pet Care">Senior Pet Care</option>
            <option value="Puppy/Kitten Care">Puppy/Kitten Care</option>
          </select>

          <button
            onClick={fetchAppointments}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center"
          >
            <FaFilter className="mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Appointments Table */}
      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Appointment #</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pet</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan="8" className="px-6 py-4 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#575CEE]"></div>
                    <span className="ml-2">Loading appointments...</span>
                  </div>
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan="8" className="px-6 py-4 text-center text-red-500">
                  {error}
                </td>
              </tr>
            ) : filteredAppointments.length === 0 ? (
              <tr>
                <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                  {searchTerm || statusFilter !== 'all' || serviceFilter !== 'all'
                    ? 'No appointments found matching your criteria.'
                    : 'No appointments found.'}
                </td>
              </tr>
            ) : (
              filteredAppointments.map((appointment) => (
                <tr key={appointment._id} className="hover:bg-gray-50">
                  {/* Appointment Number */}
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    <div className="font-semibold">{appointment.appointmentNumber || 'N/A'}</div>
                  </td>

                  {/* Customer */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <div>
                        <div className="font-medium">{appointment.customerName}</div>
                        <div className="text-gray-500 text-xs">{appointment.email}</div>
                      </div>
                    </div>
                  </td>

                  {/* Pet */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <FaPaw className="text-gray-400 mr-2" />
                      <div>
                        <div className="font-medium">{appointment.petName}</div>
                        <div className="text-gray-500 text-xs">{appointment.petType}</div>
                      </div>
                    </div>
                  </td>

                  {/* Date & Time */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">
                        {new Date(appointment.appointmentDate).toLocaleDateString()}
                      </div>
                      <div className="text-gray-500 text-xs">{appointment.appointmentTime}</div>
                    </div>
                  </td>

                  {/* Service */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="font-medium">{appointment.serviceType}</div>
                  </td>

                  {/* Status */}
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(appointment.status)}`}>
                      {appointment.status}
                    </span>
                  </td>

                  {/* Priority */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    <span className={`font-medium ${getPriorityColor(appointment.priority)}`}>
                      {appointment.priority}
                    </span>
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setSelectedAppointment(appointment)}
                      className="text-[#575CEE] hover:text-[#4a4fd1] flex items-center text-xs"
                    >
                      <FaEye className="mr-1" /> View
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Appointment Details Modal */}
      {selectedAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">Appointment Details</h3>
              <button
                onClick={() => setSelectedAppointment(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-lg mb-3 flex items-center">
                  <FaUser className="mr-2 text-[#575CEE]" />
                  Customer Information
                </h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Name:</span> {selectedAppointment.customerName}</p>
                  <p><span className="font-medium">Email:</span> {selectedAppointment.email}</p>
                  <p><span className="font-medium">Phone:</span> {selectedAppointment.phone}</p>
                  <p><span className="font-medium">Address:</span> {selectedAppointment.address}</p>
                  {selectedAppointment.emergencyContact && (
                    <p><span className="font-medium">Emergency Contact:</span> {selectedAppointment.emergencyContact}</p>
                  )}
                </div>
              </div>

              {/* Pet Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-lg mb-3 flex items-center">
                  <FaPaw className="mr-2 text-[#575CEE]" />
                  Pet Information
                </h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Name:</span> {selectedAppointment.petName}</p>
                  <p><span className="font-medium">Type:</span> {selectedAppointment.petType}</p>
                  {selectedAppointment.breed && <p><span className="font-medium">Breed:</span> {selectedAppointment.breed}</p>}
                  <p><span className="font-medium">Age:</span> {selectedAppointment.age}</p>
                  {selectedAppointment.weight && <p><span className="font-medium">Weight:</span> {selectedAppointment.weight}</p>}
                  <p><span className="font-medium">Gender:</span> {selectedAppointment.gender}</p>
                </div>
              </div>

              {/* Appointment Details */}
              <div className="bg-gray-50 p-4 rounded-lg md:col-span-2">
                <h4 className="font-semibold text-lg mb-3 flex items-center">
                  <FaCalendarAlt className="mr-2 text-[#575CEE]" />
                  Appointment Details
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <p><span className="font-medium">Appointment #:</span> {selectedAppointment.appointmentNumber}</p>
                  <p><span className="font-medium">Date:</span> {new Date(selectedAppointment.appointmentDate).toLocaleDateString()}</p>
                  <p><span className="font-medium">Time:</span> {selectedAppointment.appointmentTime}</p>
                  <p><span className="font-medium">Service:</span> {selectedAppointment.serviceType}</p>
                  <p><span className="font-medium">Status:</span>
                    <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedAppointment.status)}`}>
                      {selectedAppointment.status}
                    </span>
                  </p>
                  <p><span className="font-medium">Priority:</span>
                    <span className={`ml-2 font-medium ${getPriorityColor(selectedAppointment.priority)}`}>
                      {selectedAppointment.priority}
                    </span>
                  </p>
                  {selectedAppointment.preferredVet && (
                    <p><span className="font-medium">Preferred Vet:</span> {selectedAppointment.preferredVet}</p>
                  )}
                  {selectedAppointment.assignedVet && (
                    <p><span className="font-medium">Assigned Vet:</span> {selectedAppointment.assignedVet}</p>
                  )}
                </div>

                {/* Medical Information */}
                {(selectedAppointment.symptoms || selectedAppointment.medications || selectedAppointment.specialRequirements) && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h5 className="font-medium mb-2">Medical Information</h5>
                    {selectedAppointment.symptoms && (
                      <p className="mb-2 text-sm"><span className="font-medium">Symptoms:</span> {selectedAppointment.symptoms}</p>
                    )}
                    {selectedAppointment.medications && (
                      <p className="mb-2 text-sm"><span className="font-medium">Medications:</span> {selectedAppointment.medications}</p>
                    )}
                    {selectedAppointment.specialRequirements && (
                      <p className="text-sm"><span className="font-medium">Special Requirements:</span> {selectedAppointment.specialRequirements}</p>
                    )}
                  </div>
                )}

                {/* Admin Notes */}
                {selectedAppointment.adminNotes && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h5 className="font-medium mb-2">Admin Notes</h5>
                    <p className="text-sm text-gray-700">{selectedAppointment.adminNotes}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              {selectedAppointment.status === 'pending' && (
                <>
                  <button
                    onClick={() => handleStatusUpdate(selectedAppointment._id, 'confirmed')}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                  >
                    <FaCheck className="mr-2" />
                    Confirm
                  </button>
                  <button
                    onClick={() => handleStatusUpdate(selectedAppointment._id, 'cancelled', 'Cancelled by admin')}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
                  >
                    <FaTimes className="mr-2" />
                    Cancel
                  </button>
                </>
              )}

              {selectedAppointment.status === 'confirmed' && (
                <>
                  <button
                    onClick={() => handleStatusUpdate(selectedAppointment._id, 'in-progress')}
                    className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    Start Appointment
                  </button>
                  <button
                    onClick={() => handleStatusUpdate(selectedAppointment._id, 'cancelled', 'Cancelled by admin')}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
                  >
                    <FaTimes className="mr-2" />
                    Cancel
                  </button>
                </>
              )}

              {selectedAppointment.status === 'in-progress' && (
                <button
                  onClick={() => handleStatusUpdate(selectedAppointment._id, 'completed')}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                >
                  <FaCheck className="mr-2" />
                  Mark Complete
                </button>
              )}

              <button
                onClick={() => setSelectedAppointment(null)}
                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentsSection;
