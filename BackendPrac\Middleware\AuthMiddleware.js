const jwt = require('jsonwebtoken')

exports.AuthMiddleware = async (req, res, next) => {
  try {
    console.log('🔐 AuthMiddleware: Processing request to:', req.path);
    console.log('🔐 AuthMiddleware: Headers received:', {
      authorization: req.headers.authorization ? 'Present' : 'Missing',
      authLength: req.headers.authorization?.length,
      authStart: req.headers.authorization?.substring(0, 20) + '...'
    });

    const authHeader = req.headers.authorization;
    if (!authHeader) {
      console.log('❌ AuthMiddleware: No authorization header found');
      return res.status(401).json({
        success: false,
        code: 'TOKEN_MISSING',
        message: 'No token provided'
      });
    }

    // Extract token from Bearer format
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;
    if (!token) {
      console.log('❌ AuthMiddleware: Invalid token format');
      return res.status(401).json({
        success: false,
        code: 'INVALID_TOKEN_FORMAT',
        message: 'Invalid token format'
      });
    }

    console.log('🔐 AuthMiddleware: Token extracted, length:', token.length);

    try {
      console.log('🔐 AuthMiddleware: Attempting to verify token...');
      const decoded = await jwt.verify(token, "axzsndhsj12343563-+}{\@#$%&*'/?");

      console.log('✅ AuthMiddleware: Token verified successfully');
      console.log('🔐 AuthMiddleware: Decoded payload:', {
        id: decoded.id,
        email: decoded.email,
        name: decoded.name,
        exp: decoded.exp,
        iat: decoded.iat
      });

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      if (decoded.exp && decoded.exp < now) {
        console.log('❌ AuthMiddleware: Token expired');
        console.log('🕐 AuthMiddleware: Current time:', now, 'Token exp:', decoded.exp);
        return res.status(401).json({
          success: false,
          code: 'TOKEN_EXPIRED',
          message: 'Token has expired, please log in again'
        });
      }

      console.log('✅ AuthMiddleware: Token is valid and not expired');
      // Add user info to request
      req.user = decoded;
      next();
    } catch (jwtError) {
      console.log('❌ AuthMiddleware: JWT verification failed');
      console.log('❌ AuthMiddleware: JWT Error:', jwtError.name, jwtError.message);

      if (jwtError.name === 'TokenExpiredError') {
        console.log('❌ AuthMiddleware: Token expired error');
        return res.status(401).json({
          success: false,
          code: 'TOKEN_EXPIRED',
          message: 'Token has expired, please log in again'
        });
      }

      console.log('❌ AuthMiddleware: Invalid token error');
      return res.status(401).json({
        success: false,
        code: 'INVALID_TOKEN',
        message: 'Invalid token'
      });
    }
  } catch (error) {
    console.error('❌ AuthMiddleware: Unexpected error:', error);
    console.error('❌ AuthMiddleware: Error stack:', error.stack);
    return res.status(500).json({
      success: false,
      code: 'AUTH_ERROR',
      message: 'Authentication failed'
    });
  }
}