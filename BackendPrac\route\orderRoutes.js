const express = require('express');
const router = express.Router();
const {
    createOrder,
    getAllOrders,
    getUserOrders,
    getOrderById,
    updateOrderStatus,
    deleteOrder,
    getOrderStats
} = require('../controller/rawDataController');
const { AuthMiddleware } = require('../Middleware/AuthMiddleware');

// Public routes (for order creation from payment success)
router.post('/create', createOrder);

// Test route for authentication debugging
router.get('/test-auth', AuthMiddleware, (req, res) => {
  console.log('✅ Test auth endpoint reached successfully');
  console.log('✅ User from token:', req.user);
  res.json({
    success: true,
    message: 'Authentication working correctly',
    user: req.user,
    timestamp: new Date().toISOString()
  });
});

// Protected routes (require authentication)
router.get('/user/:userId', AuthMiddleware, getUserOrders);
router.get('/stats', AuthMiddleware, getOrderStats);
router.get('/all', AuthMiddleware, getAllOrders);
router.get('/:orderId', AuthMiddleware, getOrderById);

// Admin only routes (require admin authorization)
router.put('/:orderId/status', AuthMiddleware, updateOrderStatus);
router.delete('/:orderId', AuthMiddleware, deleteOrder);

module.exports = router;
