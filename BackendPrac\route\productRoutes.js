const express = require('express');
const router = express.Router();
const upload = require('../Middleware/Multer');
const {
    createProduct,
    getAllProducts,
    getProductById,
    updateProduct,
    deleteProduct,
    getProductStats
} = require('../controller/rawDataController');

// Product CRUD Routes
router.post('/create', upload.single('image'), createProduct);           // Create product
router.get('/all', getAllProducts);                                      // Get all products (with filters)
router.get('/stats', getProductStats);                                   // Get product statistics
router.get('/:productId', getProductById);                               // Get single product
router.put('/:productId', upload.single('image'), updateProduct);       // Update product
router.delete('/:productId', deleteProduct);                            // Delete product (soft delete)

module.exports = router;