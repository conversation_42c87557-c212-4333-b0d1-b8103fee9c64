import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v0';

// Get auth token from localStorage
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/appointments`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const appointmentService = {
  // Create new appointment
  createAppointment: async (appointmentData) => {
    try {
      console.log('📅 Creating appointment:', appointmentData);
      const response = await apiClient.post('/create', appointmentData);
      console.log('✅ Appointment created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating appointment:', error);
      throw error;
    }
  },

  // Get user's appointments
  getUserAppointments: async (params = {}) => {
    try {
      console.log('📅 Fetching user appointments with params:', params);
      const response = await apiClient.get('/my-appointments', { params });
      console.log('✅ User appointments fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching user appointments:', error);
      throw error;
    }
  },

  // Get all appointments (admin)
  getAllAppointments: async (params = {}) => {
    try {
      console.log('📅 Fetching all appointments with params:', params);
      const response = await apiClient.get('/', { params });
      console.log('✅ All appointments fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching all appointments:', error);
      throw error;
    }
  },

  // Get appointment by ID
  getAppointmentById: async (appointmentId) => {
    try {
      console.log('📅 Fetching appointment by ID:', appointmentId);
      const response = await apiClient.get(`/${appointmentId}`);
      console.log('✅ Appointment fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching appointment:', error);
      throw error;
    }
  },

  // Update appointment status (admin)
  updateAppointmentStatus: async (appointmentId, statusData) => {
    try {
      console.log('📅 Updating appointment status:', { appointmentId, statusData });
      const response = await apiClient.put(`/${appointmentId}/status`, statusData);
      console.log('✅ Appointment status updated:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating appointment status:', error);
      throw error;
    }
  },

  // Reschedule appointment (admin)
  rescheduleAppointment: async (appointmentId, rescheduleData) => {
    try {
      console.log('📅 Rescheduling appointment:', { appointmentId, rescheduleData });
      const response = await apiClient.put(`/${appointmentId}/reschedule`, rescheduleData);
      console.log('✅ Appointment rescheduled:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error rescheduling appointment:', error);
      throw error;
    }
  },

  // Delete appointment (admin)
  deleteAppointment: async (appointmentId) => {
    try {
      console.log('📅 Deleting appointment:', appointmentId);
      const response = await apiClient.delete(`/${appointmentId}`);
      console.log('✅ Appointment deleted:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error deleting appointment:', error);
      throw error;
    }
  },

  // Get appointment statistics (admin)
  getAppointmentStats: async () => {
    try {
      console.log('📅 Fetching appointment statistics...');
      const response = await apiClient.get('/admin/stats');
      console.log('✅ Appointment stats fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching appointment stats:', error);
      throw error;
    }
  },

  // Test authentication
  testAuth: async () => {
    try {
      const response = await apiClient.get('/my-appointments', { 
        params: { page: 1, limit: 1 } 
      });
      return response.data;
    } catch (error) {
      console.error('❌ Auth test failed:', error);
      throw error;
    }
  }
};

export default appointmentService;
