import axios from 'axios';
import { getAuthHeaders, debugAuthState, clearAuthData } from '../utils/authUtils';

const API_BASE_URL = 'http://localhost:3000/api/v0';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const authHeaders = getAuthHeaders();
    config.headers = { ...config.headers, ...authHeaders };

    console.log('🚀 API Request:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      hasAuth: !!config.headers.Authorization
    });

    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.response?.data?.message,
      code: error.response?.data?.code
    });

    // Handle authentication errors
    if (error.response?.status === 401) {
      console.warn('🔐 Authentication failed - clearing auth data');
      debugAuthState();

      // Don't clear auth data immediately, let the component handle it
      // clearAuthData();
      // window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

export const orderService = {
  // Test authentication endpoint
  testAuth: async () => {
    try {
      console.log('🧪 Testing authentication...');
      debugAuthState();
      const response = await api.get('/orders/test-auth');
      console.log('✅ Auth test successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Auth test failed:', error);
      throw error;
    }
  },

  // Get all orders (admin)
  getAllOrders: async (params = {}) => {
    try {
      debugAuthState();
      const response = await api.get('/orders/all', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all orders:', error);
      throw error;
    }
  },

  // Get user orders
  getUserOrders: async (userId, params = {}) => {
    try {
      debugAuthState();
      if (!userId) {
        throw new Error('User ID is required');
      }
      const response = await api.get(`/orders/user/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw error;
    }
  },

  // Get single order
  getOrderById: async (orderId) => {
    try {
      debugAuthState();
      const response = await api.get(`/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  },

  // Update order status
  updateOrderStatus: async (orderId, status) => {
    try {
      debugAuthState();
      const response = await api.put(`/orders/${orderId}/status`, { status });
      return response.data;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  },

  // Create order
  createOrder: async (orderData) => {
    try {
      debugAuthState();
      const response = await api.post('/orders/create', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  // Get order statistics
  getOrderStats: async () => {
    try {
      debugAuthState();
      const response = await api.get('/orders/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw error;
    }
  }
};

export default orderService;
