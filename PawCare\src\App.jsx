import './App.css'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import About from './Components/About/About.jsx'
import Home from './Components/Home/Home.jsx'
import VetClinics from './Components/VetClinics/VetClinics.jsx'
import Shelter from './Components/Shelter/Shelter.jsx'
import ShelterDonation from './Components/Shelter/ShelterDonation.jsx'
import AdoptionPage from './Components/Shelter/AdoptionPage.jsx'
import ShopView from './Components/ShopView/ShopView.jsx'
import Login from './Components/Auth/Login.jsx'
import Signup from './Components/Auth/Signup.jsx'
import Cart from './Components/Cart/Cart.jsx'
import ProtectedRoute from './Components/Auth/ProtectedRoute.jsx'
import AppLayout from './Components/Layout/AppLayout.jsx'
import { CartProvider } from './context/CartContext.jsx'
import CatShop from './Components/CatShop/CatShop.jsx'
import Beds from './Components/Beds/Beds.jsx'
import Toys from './Components/Toys/Toys.jsx'
import Bath from './Components/Bath/Bath.jsx'
import Food from './Components/Food/Food.jsx'
import Treats from './Components/Treats/Treats.jsx'
import Furniture from './Components/Furniture/Furniture.jsx'
import SystemAdminDashboard from './Components/Admin/AdminWrapper.jsx'
import OrderHistory from './Components/Orders/OrderHistory.jsx'
import OnlineAppointmentBooking from './Components/OnlineAppointment/OnlineAppointmentBooking.jsx'
import PageNotFound from './Components/Error/PageNotFound.jsx'
import ErrorBoundary from './Components/Error/ErrorBoundary.jsx'

// Stripe imports
import { loadStripe } from '@stripe/stripe-js'
import { Elements } from '@stripe/react-stripe-js'

// Use your actual test key from Stripe dashboard
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4');

function App() {
  return (
    <ErrorBoundary>
      <Elements stripe={stripePromise}>
        <CartProvider>
          <BrowserRouter>
            <AppLayout>
              <Routes>
              {/* Auth Routes */}
              <Route path='/login' element={<Login />} />
              <Route path='/signup' element={<Signup />} />
              <Route path='/' element={<Navigate to="/login" />} />

              {/* Protected Routes */}
              <Route path='/home' element={<ProtectedRoute><Home /></ProtectedRoute>} />
              <Route path='/about' element={<ProtectedRoute><About /></ProtectedRoute>} />
              <Route path='/admin' element={<SystemAdminDashboard />} />
              <Route path='/vet-clinics' element={<ProtectedRoute><VetClinics /></ProtectedRoute>} />
              <Route path='/shelters' element={<ProtectedRoute><Shelter /></ProtectedRoute>} />
              <Route path='/shelter-donation/:shelterId' element={<ProtectedRoute><ShelterDonation /></ProtectedRoute>} />
              <Route path='/shelter-adoption' element={<ProtectedRoute><AdoptionPage /></ProtectedRoute>} />
              <Route path='/shopview' element={<ProtectedRoute><ShopView /></ProtectedRoute>} />
              <Route path='/cart' element={<ProtectedRoute><Cart /></ProtectedRoute>} />
              <Route path='/orders' element={<ProtectedRoute><OrderHistory /></ProtectedRoute>} />
              <Route path='/online-appointment' element={<ProtectedRoute><OnlineAppointmentBooking /></ProtectedRoute>} />

              <Route path='/CatShop' element={<ProtectedRoute><CatShop/></ProtectedRoute>}></Route>
              <Route path='/beds' element={<ProtectedRoute><Beds/></ProtectedRoute>}></Route>
              <Route path='/toys' element={<ProtectedRoute><Toys/></ProtectedRoute>}></Route>
              <Route path='/bath' element={<ProtectedRoute><Bath/></ProtectedRoute>}></Route>
              <Route path='/food' element={<ProtectedRoute><Food/></ProtectedRoute>}></Route>
              <Route path='/treats' element={<ProtectedRoute><Treats/></ProtectedRoute>}></Route>
              <Route path='/furniture' element={<ProtectedRoute><Furniture/></ProtectedRoute>}></Route>

              {/* Catch-all route for 404 errors */}
              <Route path='*' element={<PageNotFound />} />
            </Routes>
          </AppLayout>
        </BrowserRouter>
      </CartProvider>
      </Elements>
    </ErrorBoundary>
  )
}

export default App
