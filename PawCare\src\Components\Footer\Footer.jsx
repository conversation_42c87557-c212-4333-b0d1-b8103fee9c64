import React from 'react'
import { FaInstagram, FaTwitter, FaFacebook, FaLinkedin, FaPhone, FaEnvelope, FaMapMarkerAlt, FaPaw } from 'react-icons/fa'
import { Link } from 'react-router-dom'

function FooterComponent() {
  return (
    <footer className="w-full bg-gradient-to-br from-[#575CEE] via-[#4A51E8] to-[#3D44E2] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 text-6xl text-white">
          <FaPaw className="rotate-12" />
        </div>
        <div className="absolute bottom-10 right-10 text-4xl text-white">
          <FaPaw className="-rotate-12" />
        </div>
        <div className="absolute top-1/2 left-1/4 text-3xl text-white">
          <FaPaw className="rotate-45" />
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">

          {/* Brand Section */}
          <div className="lg:col-span-1 space-y-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <FaPaw className="text-white text-2xl" />
                <h1 className="text-white text-3xl font-bold tracking-wide">Paw Care</h1>
              </div>
              <p className="text-blue-100 text-base leading-relaxed max-w-sm">
                Providing exceptional care and premium products for your beloved pets. Your pet's health and happiness is our priority.
              </p>
            </div>

            {/* Social Media Links */}
            <div className="space-y-3">
              <h3 className="text-white font-semibold text-lg">Follow Us</h3>
              <div className="flex space-x-4">
                <a
                  href="https://www.instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group bg-white/10 backdrop-blur-sm p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 ease-in-out"
                  aria-label="Follow us on Instagram"
                >
                  <FaInstagram className="text-white group-hover:text-[#575CEE] text-xl transition-colors duration-300" />
                </a>

                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group bg-white/10 backdrop-blur-sm p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 ease-in-out"
                  aria-label="Follow us on Twitter"
                >
                  <FaTwitter className="text-white group-hover:text-[#575CEE] text-xl transition-colors duration-300" />
                </a>

                <a
                  href="https://www.facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group bg-white/10 backdrop-blur-sm p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 ease-in-out"
                  aria-label="Follow us on Facebook"
                >
                  <FaFacebook className="text-white group-hover:text-[#575CEE] text-xl transition-colors duration-300" />
                </a>

                <a
                  href="https://www.linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group bg-white/10 backdrop-blur-sm p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 ease-in-out"
                  aria-label="Follow us on LinkedIn"
                >
                  <FaLinkedin className="text-white group-hover:text-[#575CEE] text-xl transition-colors duration-300" />
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links Section */}
          <div className="space-y-6">
            <h3 className="text-white font-semibold text-xl border-b border-white/20 pb-2">Quick Links</h3>
            <nav className="space-y-3">
              <Link
                to="/about"
                className="block text-blue-100 hover:text-white hover:translate-x-2 transition-all duration-300 ease-in-out text-base"
              >
                About Us
              </Link>
              <Link
                to="/shopview"
                className="block text-blue-100 hover:text-white hover:translate-x-2 transition-all duration-300 ease-in-out text-base"
              >
                Products & Shop
              </Link>
              <Link
                to="/vet-clinics"
                className="block text-blue-100 hover:text-white hover:translate-x-2 transition-all duration-300 ease-in-out text-base"
              >
                Vet Clinics
              </Link>
              <Link
                to="/shelters"
                className="block text-blue-100 hover:text-white hover:translate-x-2 transition-all duration-300 ease-in-out text-base"
              >
                Pet Shelter
              </Link>
            </nav>
          </div>

          {/* Service Areas Section */}
          <div className="space-y-6">
            <h3 className="text-white font-semibold text-xl border-b border-white/20 pb-2">Service Areas</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-blue-100">
                <FaMapMarkerAlt className="text-white flex-shrink-0" />
                <span className="text-base">Peshawar</span>
              </div>
              <div className="flex items-center space-x-3 text-blue-100">
                <FaMapMarkerAlt className="text-white flex-shrink-0" />
                <span className="text-base">Islamabad</span>
              </div>
              <p className="text-blue-200 text-sm mt-4 italic">
                Expanding to more cities soon!
              </p>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="space-y-6">
            <h3 className="text-white font-semibold text-xl border-b border-white/20 pb-2">Get In Touch</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 text-blue-100">
                <FaPhone className="text-white flex-shrink-0" />
                <span className="text-base">+92 337 0399 050</span>
              </div>
              <div className="flex items-center space-x-3 text-blue-100">
                <FaEnvelope className="text-white flex-shrink-0" />
                <span className="text-base break-all"><EMAIL></span>
              </div>
            </div>
          </div>

        </div>

        {/* Footer Bottom */}
        <div className="mt-12 pt-8 border-t border-white/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-blue-100 text-sm text-center md:text-left">
              © 2025 Paw Care. All rights reserved. Made with ❤️ for pet lovers.
            </p>
            <div className="flex space-x-6 text-sm">
              <button className="text-blue-100 hover:text-white transition-colors duration-300">
                Privacy Policy
              </button>
              <button className="text-blue-100 hover:text-white transition-colors duration-300">
                Terms of Service
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default FooterComponent
