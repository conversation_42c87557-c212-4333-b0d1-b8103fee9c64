import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  FaSearch, 
  FaArrowLeft, 
  FaPaw, 
  FaHeart, 
  FaGraduationCap, 
  FaBrain, 
  FaUtensils, 
  FaHospital, 
  FaCut, 
  FaShieldAlt, 
  FaHome,
  FaClock,
  FaUser,
  FaBookOpen,
  FaLightbulb,
  FaCheckCircle
} from 'react-icons/fa';
import { guideArticles, guideCategories, additionalGuides } from '../../data/guideData';

const PetGuide = () => {
  const { guideId } = useParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedGuide, setSelectedGuide] = useState(null);

  // Combine all guides
  const allGuides = [...guideArticles, ...additionalGuides];

  useEffect(() => {
    if (guideId) {
      const guide = allGuides.find(g => g.id === parseInt(guideId));
      setSelectedGuide(guide);
    }
  }, [guideId]);

  // Filter guides based on search and category
  const filteredGuides = allGuides.filter(guide => {
    const matchesSearch = !searchTerm || 
      guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.authorName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || guide.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getCategoryIcon = (categoryId) => {
    const icons = {
      all: <FaPaw />,
      basics: <FaHeart />,
      training: <FaGraduationCap />,
      behavior: <FaBrain />,
      nutrition: <FaUtensils />,
      health: <FaHospital />,
      grooming: <FaCut />,
      safety: <FaShieldAlt />,
      adoption: <FaHome />
    };
    return icons[categoryId] || <FaPaw />;
  };

  const handleGuideClick = (guide) => {
    if (guide.content) {
      setSelectedGuide(guide);
      navigate(`/pet-guide/${guide.id}`);
    } else {
      // For guides without full content, show a preview
      alert(`${guide.title} - Full content coming soon!\n\n${guide.description}`);
    }
  };

  const handleBackToGuides = () => {
    setSelectedGuide(null);
    navigate('/pet-guide');
  };

  // Render individual guide content
  if (selectedGuide && selectedGuide.content) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between">
              <button
                onClick={handleBackToGuides}
                className="flex items-center text-[#575CEE] hover:text-[#4a4fd1] transition-colors duration-200"
              >
                <FaArrowLeft className="mr-2" />
                Back to Guides
              </button>
              <div className="flex items-center text-sm text-gray-500">
                <FaClock className="mr-1" />
                {selectedGuide.readTime}
              </div>
            </div>
          </div>

          {/* Guide Content */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            {/* Hero Section */}
            <div 
              className="h-64 bg-cover bg-center relative"
              style={{ backgroundImage: `url(${selectedGuide.backgroundImage})` }}
            >
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-end">
                <div className="p-8 text-white">
                  <h1 className="text-4xl font-bold mb-2">{selectedGuide.title}</h1>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <FaUser className="mr-2" />
                      <span>{selectedGuide.authorName}</span>
                    </div>
                    <div className="flex items-center">
                      <FaBookOpen className="mr-2" />
                      <span>{selectedGuide.category}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Body */}
            <div className="p-8">
              {/* Introduction */}
              <div className="mb-8">
                <p className="text-lg text-gray-700 leading-relaxed">
                  {selectedGuide.content.introduction}
                </p>
              </div>

              {/* Sections */}
              <div className="space-y-8">
                {selectedGuide.content.sections.map((section, index) => (
                  <div key={index} className="border-l-4 border-[#575CEE] pl-6">
                    <h2 className="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                      <FaLightbulb className="mr-3 text-[#575CEE]" />
                      {section.title}
                    </h2>
                    <p className="text-gray-700 mb-4 leading-relaxed">
                      {section.content}
                    </p>
                    
                    {section.tips && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                          <FaCheckCircle className="mr-2 text-green-500" />
                          Key Tips:
                        </h3>
                        <ul className="space-y-2">
                          {section.tips.map((tip, tipIndex) => (
                            <li key={tipIndex} className="flex items-start">
                              <span className="text-[#575CEE] mr-2 mt-1">•</span>
                              <span className="text-gray-700">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Call to Action */}
              <div className="mt-12 bg-gradient-to-r from-[#575CEE] to-[#4a4fd1] rounded-lg p-6 text-white text-center">
                <h3 className="text-xl font-semibold mb-2">Need Professional Help?</h3>
                <p className="mb-4">Our veterinary experts are here to help with personalized advice.</p>
                <button
                  onClick={() => navigate('/online-appointment')}
                  className="bg-white text-[#575CEE] px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
                >
                  Book an Appointment
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render guides overview
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4 flex items-center justify-center">
            <FaPaw className="mr-3 text-[#575CEE]" />
            Pet Care Guide
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive guides to help you provide the best care for your beloved pets. 
            From basic care to advanced training, we've got you covered.
          </p>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search guides..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div className="md:w-64">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
              >
                {guideCategories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Category Pills */}
        <div className="flex flex-wrap gap-3 mb-8 justify-center">
          {guideCategories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center px-4 py-2 rounded-full transition-all duration-200 ${
                selectedCategory === category.id
                  ? 'bg-[#575CEE] text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-100 shadow'
              }`}
            >
              {getCategoryIcon(category.id)}
              <span className="ml-2">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Guides Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredGuides.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <FaSearch className="mx-auto text-4xl text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No guides found</h3>
              <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            filteredGuides.map(guide => (
              <div
                key={guide.id}
                onClick={() => handleGuideClick(guide)}
                className="bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-300 hover:shadow-xl"
              >
                <div 
                  className="h-48 bg-cover bg-center relative"
                  style={{ backgroundImage: `url(${guide.backgroundImage})` }}
                >
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                    <div className="p-4 text-white">
                      <div className="flex items-center space-x-2 text-sm mb-2">
                        {getCategoryIcon(guide.category)}
                        <span className="capitalize">{guide.category}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    {guide.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {guide.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center">
                      <FaUser className="mr-1" />
                      <span>{guide.authorName}</span>
                    </div>
                    <div className="flex items-center">
                      <FaClock className="mr-1" />
                      <span>{guide.readTime}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Call to Action */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Still Have Questions?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our experienced veterinarians and pet care specialists are here to help. 
            Book an appointment for personalized advice and care for your pet.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => navigate('/online-appointment')}
              className="bg-[#575CEE] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#4a4fd1] transition-colors duration-200"
            >
              Book Appointment
            </button>
            <button
              onClick={() => navigate('/vet-clinics')}
              className="bg-white text-[#575CEE] border-2 border-[#575CEE] px-6 py-3 rounded-lg font-semibold hover:bg-[#575CEE] hover:text-white transition-colors duration-200"
            >
              Find Vet Clinics
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PetGuide;
