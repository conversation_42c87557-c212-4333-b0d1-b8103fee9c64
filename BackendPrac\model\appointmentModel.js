const mongoose = require('mongoose');

const AppointmentSchema = new mongoose.Schema({
    // Customer Information
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User is required']
    },
    appointmentNumber: {
        type: String,
        unique: true,
        required: true
    },
    customerName: {
        type: String,
        required: [true, 'Customer name is required'],
        trim: true,
        maxlength: [100, 'Customer name cannot exceed 100 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        trim: true,
        lowercase: true,
        match: [/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address']
    },
    phone: {
        type: String,
        required: [true, 'Phone number is required'],
        trim: true,
        match: [/^[\d\s\-\(\)\+]+$/, 'Please enter a valid phone number']
    },
    address: {
        type: String,
        required: [true, 'Address is required'],
        trim: true,
        maxlength: [500, 'Address cannot exceed 500 characters']
    },
    emergencyContact: {
        type: String,
        trim: true,
        match: [/^[\d\s\-\(\)\+]*$/, 'Please enter a valid emergency contact number']
    },

    // Pet Information
    petName: {
        type: String,
        required: [true, 'Pet name is required'],
        trim: true,
        maxlength: [50, 'Pet name cannot exceed 50 characters']
    },
    petType: {
        type: String,
        required: [true, 'Pet type is required'],
        enum: {
            values: ['Dog', 'Cat', 'Bird', 'Rabbit', 'Hamster', 'Fish', 'Other'],
            message: '{VALUE} is not a valid pet type'
        }
    },
    breed: {
        type: String,
        trim: true,
        maxlength: [50, 'Breed cannot exceed 50 characters']
    },
    age: {
        type: String,
        required: [true, 'Pet age is required'],
        trim: true,
        maxlength: [20, 'Age cannot exceed 20 characters']
    },
    weight: {
        type: String,
        trim: true,
        maxlength: [20, 'Weight cannot exceed 20 characters']
    },
    gender: {
        type: String,
        required: [true, 'Pet gender is required'],
        enum: {
            values: ['Male', 'Female'],
            message: '{VALUE} is not a valid gender'
        }
    },

    // Appointment Details
    appointmentDate: {
        type: Date,
        required: [true, 'Appointment date is required'],
        validate: {
            validator: function(date) {
                return date >= new Date().setHours(0, 0, 0, 0);
            },
            message: 'Appointment date must be in the future'
        }
    },
    appointmentTime: {
        type: String,
        required: [true, 'Appointment time is required'],
        enum: {
            values: [
                '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
                '12:00 PM', '12:30 PM', '02:00 PM', '02:30 PM', '03:00 PM', '03:30 PM',
                '04:00 PM', '04:30 PM', '05:00 PM', '05:30 PM', '06:00 PM'
            ],
            message: '{VALUE} is not a valid appointment time'
        }
    },
    serviceType: {
        type: String,
        required: [true, 'Service type is required'],
        enum: {
            values: [
                'General Checkup', 'Vaccination', 'Emergency Care', 'Dental Care',
                'Surgery Consultation', 'Grooming', 'Behavioral Consultation',
                'Nutrition Consultation', 'Senior Pet Care', 'Puppy/Kitten Care'
            ],
            message: '{VALUE} is not a valid service type'
        }
    },
    preferredVet: {
        type: String,
        enum: {
            values: ['', 'Dr. Sarah Johnson', 'Dr. Michael Chen', 'Dr. Emily Rodriguez', 'Dr. David Thompson'],
            message: '{VALUE} is not a valid veterinarian'
        }
    },

    // Medical Information
    symptoms: {
        type: String,
        trim: true,
        maxlength: [1000, 'Symptoms description cannot exceed 1000 characters']
    },
    previousVisits: {
        type: String,
        trim: true,
        maxlength: [500, 'Previous visits information cannot exceed 500 characters']
    },
    medications: {
        type: String,
        trim: true,
        maxlength: [500, 'Medications information cannot exceed 500 characters']
    },
    specialRequirements: {
        type: String,
        trim: true,
        maxlength: [1000, 'Special requirements cannot exceed 1000 characters']
    },
    notes: {
        type: String,
        trim: true,
        maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },

    // Appointment Status and Management
    status: {
        type: String,
        enum: {
            values: ['pending', 'confirmed', 'in-progress', 'completed', 'cancelled', 'rescheduled'],
            message: '{VALUE} is not a valid appointment status'
        },
        default: 'pending'
    },
    priority: {
        type: String,
        enum: {
            values: ['low', 'normal', 'high', 'urgent'],
            message: '{VALUE} is not a valid priority level'
        },
        default: 'normal'
    },
    assignedVet: {
        type: String,
        trim: true
    },
    estimatedDuration: {
        type: Number, // in minutes
        default: 30,
        min: [15, 'Appointment duration must be at least 15 minutes'],
        max: [240, 'Appointment duration cannot exceed 240 minutes']
    },

    // Admin Notes and Management
    adminNotes: {
        type: String,
        trim: true,
        maxlength: [1000, 'Admin notes cannot exceed 1000 characters']
    },
    cancellationReason: {
        type: String,
        trim: true,
        maxlength: [500, 'Cancellation reason cannot exceed 500 characters']
    },
    rescheduledFrom: {
        originalDate: Date,
        originalTime: String,
        reason: String
    },

    // Timestamps and Tracking
    confirmedAt: {
        type: Date
    },
    completedAt: {
        type: Date
    },
    cancelledAt: {
        type: Date
    },
    lastUpdatedBy: {
        type: String,
        trim: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better query performance
AppointmentSchema.index({ appointmentDate: 1, appointmentTime: 1 });
AppointmentSchema.index({ user: 1 });
AppointmentSchema.index({ status: 1 });
AppointmentSchema.index({ appointmentNumber: 1 });
AppointmentSchema.index({ email: 1 });
AppointmentSchema.index({ petType: 1 });
AppointmentSchema.index({ serviceType: 1 });

// Virtual for formatted appointment date and time
AppointmentSchema.virtual('formattedDateTime').get(function() {
    if (this.appointmentDate && this.appointmentTime) {
        const date = new Date(this.appointmentDate);
        return `${date.toLocaleDateString()} at ${this.appointmentTime}`;
    }
    return '';
});

// Virtual for appointment duration in hours
AppointmentSchema.virtual('durationHours').get(function() {
    return this.estimatedDuration ? (this.estimatedDuration / 60).toFixed(1) : '0.5';
});

// Pre-save middleware to generate appointment number
AppointmentSchema.pre('save', async function(next) {
    if (this.isNew) {
        try {
            // Generate unique appointment number
            const date = new Date();
            const year = date.getFullYear().toString().slice(-2);
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            
            // Find the last appointment number for today
            const lastAppointment = await this.constructor.findOne({
                appointmentNumber: new RegExp(`^APT${year}${month}${day}`)
            }).sort({ appointmentNumber: -1 });
            
            let sequence = 1;
            if (lastAppointment) {
                const lastSequence = parseInt(lastAppointment.appointmentNumber.slice(-3));
                sequence = lastSequence + 1;
            }
            
            this.appointmentNumber = `APT${year}${month}${day}${sequence.toString().padStart(3, '0')}`;
        } catch (error) {
            return next(error);
        }
    }
    next();
});

// Static method to get appointment statistics
AppointmentSchema.statics.getStats = async function() {
    const stats = await this.aggregate([
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 }
            }
        }
    ]);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const todayAppointments = await this.countDocuments({
        appointmentDate: {
            $gte: today,
            $lt: tomorrow
        }
    });
    
    return {
        byStatus: stats,
        todayCount: todayAppointments
    };
};

module.exports = mongoose.model('Appointment', AppointmentSchema);
