import React from 'react';
import Slider from 'react-slick';
import AuthorCard from '../Card/AuthorCard';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

// Import slick carousel CSS
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

// Custom arrow components
const PrevArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={`${className} z-10`}
      style={{
        ...style,
        background: '#575CEE',
        borderRadius: '50%',
        width: '30px',
        height: '30px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        left: '-15px'
      }}
      onClick={onClick}
    >
      <FaChevronLeft className="text-white" />
    </div>
  );
};

const NextArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={`${className} z-10`}
      style={{
        ...style,
        background: '#575CEE',
        borderRadius: '50%',
        width: '30px',
        height: '30px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        right: '-15px'
      }}
      onClick={onClick}
    >
      <FaChevronRight className="text-white" />
    </div>
  );
};

const GuideSlider = ({ guides, title }) => {
  // Slider settings
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    prevArrow: <PrevArrow />,
    nextArrow: <NextArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };

  return (
    <div className="px-10 py-8">
      <h1 className='text-center text-3xl font-bold mb-8'>{title}</h1>
      <div className="relative">
        <Slider {...settings}>
          {guides.map((guide) => (
            <div key={guide.id} className="px-2">
              <AuthorCard
                id={guide.id}
                authorName={guide.authorName}
                backgroundImage={guide.backgroundImage}
                title={guide.title}
                description={guide.description}
                readTime={guide.readTime}
              />
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default GuideSlider;
