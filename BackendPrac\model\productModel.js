const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Product name is required'],
        trim: true,
        maxlength: [100, 'Product name cannot exceed 100 characters']
    },
    price: {
        type: Number,
        required: [true, 'Product price is required'],
        min: [0, 'Price cannot be negative']
    },
    description: {
        type: String,
        required: [true, 'Product description is required'],
        trim: true,
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    image: {
        type: String,
        default: ''
    },
    category: {
        type: String,
        required: [true, 'Product category is required'],
        enum: {
            values: [
                'toys', 'food', 'accessories', 'health', 'grooming', 'bedding', 'other', 'demo',
                // Legacy support for existing categories
                'Pet Food', 'Pet Toys', 'Pet Accessories', 'Pet Health', 'Pet Grooming', 'Pet Bedding', 'Other'
            ],
            message: '{VALUE} is not a valid category'
        },
        default: 'demo'
    },
    stock: {
        type: Number,
        required: [true, 'Stock quantity is required'],
        min: [0, 'Stock cannot be negative'],
        default: 0
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    deletedAt: {
        type: Date,
        default: null
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for product status
ProductSchema.virtual('status').get(function() {
    if (this.isDeleted) return 'deleted';
    if (this.stock === 0) return 'out_of_stock';
    if (this.stock < 10) return 'low_stock';
    return 'in_stock';
});

// Index for efficient querying
ProductSchema.index({ category: 1, isDeleted: 1 });
ProductSchema.index({ name: 'text', description: 'text' });

// Static method to find active products
ProductSchema.statics.findActive = function(filter = {}) {
    return this.find({ ...filter, isDeleted: false });
};

// Instance method for soft delete
ProductSchema.methods.softDelete = function() {
    this.isDeleted = true;
    this.deletedAt = new Date();
    return this.save();
};

// Instance method to restore
ProductSchema.methods.restore = function() {
    this.isDeleted = false;
    this.deletedAt = null;
    return this.save();
};

module.exports = mongoose.model('Product', ProductSchema);