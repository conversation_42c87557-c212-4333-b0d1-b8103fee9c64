import React, { useState, useEffect } from 'react'
import { FaPaw, FaUserCircle, FaSignOutAlt } from "react-icons/fa";
import { BookingModal } from '../../Components/BookingModal/BookingModal';
import CartIcon from '../../Components/Cart/CartIcon';
import { Link, useNavigate, useLocation } from 'react-router-dom'

function HeaderNav() {
  const navigate = useNavigate();
  const location = useLocation();
  const [user, setUser] = useState(null);

  const isAdminPage = location.pathname === '/admin';

  console.log('🔍 Header: Rendering with user state', { user, isAdminPage, pathname: location.pathname });

  // Function to check and update authentication state
  const checkAuthState = () => {
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    const userName = localStorage.getItem('userName');
    const userEmail = localStorage.getItem('userEmail');

    console.log('🔍 Header: Checking auth state', {
      isAuthenticated: !!isAuthenticated,
      userName,
      userEmail
    });

    if (isAuthenticated != null) {
      const userData = {
        name: userName,
        email: userEmail
      };
      setUser(userData);
      console.log('✅ Header: User set to authenticated state', userData);
    } else {
      setUser(null);
      console.log('❌ Header: User set to null (logged out)');
    }
  };

  useEffect(() => {
    // Check auth state on mount
    checkAuthState();

    // Listen for custom auth state change events
    const handleAuthChange = () => {
      console.log('🔄 Header: Auth state change detected, updating header');
      checkAuthState();
    };

    // Add event listener for auth state changes
    window.addEventListener('authStateChanged', handleAuthChange);

    // Listen for storage changes (for multi-tab synchronization)
    const handleStorageChange = (e) => {
      if (e.key === 'isAuthenticated' || e.key === 'userName' || e.key === 'userEmail') {
        console.log('🔄 Header: Storage change detected, updating header');
        checkAuthState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('authStateChanged', handleAuthChange);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const handleLogout = () => {
    console.log('🚀 Header: Logout initiated');

    // Clear all authentication data
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('token');
    localStorage.removeItem('userName');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userId');
    localStorage.removeItem('lastOrderNumber'); // Clear order data too

    // Update local state
    setUser(null);

    // Dispatch custom event to notify other components
    window.dispatchEvent(new CustomEvent('authStateChanged', {
      detail: { type: 'logout' }
    }));

    console.log('✅ Header: Logout completed, redirecting to login');

    // Redirect to login
    navigate('/login');
  };

  return (
    <>
      <section className='flex justify-between w-full h-8 items-center text-center text-white mx-4'>
        <div>
          <FaPaw className='text-2xl text-white' />
        </div>

        {!isAdminPage ? (
          <div className='ml-2'>
            <ul className='flex justify-between gap-6'>
              <li>
                <Link
                  to={'/home'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/home'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Home
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/home'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/about'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/about'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Shop
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/about'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/vet-clinics'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/vet-clinics'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Vet-Clinics
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/vet-clinics'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/shelters'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname === '/shelters'
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Shelters
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname === '/shelters'
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
              <li>
                <Link
                  to={'/pet-guide'}
                  className={`nav-link text-sm relative px-1 py-2 group ${
                    location.pathname.startsWith('/pet-guide')
                      ? 'text-yellow-300 font-medium'
                      : 'text-white hover:text-white/90'
                  }`}
                >
                  Pet Guide
                  <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                    location.pathname.startsWith('/pet-guide')
                      ? 'scale-x-100'
                      : 'scale-x-0 group-hover:scale-x-100'
                  }`}></span>
                </Link>
              </li>
            </ul>
          </div>
        ) : (
          <div className='ml-2 flex items-center gap-4'>
            <h1 className='text-white text-lg font-semibold'>Admin Dashboard</h1>
            <Link
              to={'/home'}
              className="text-white text-sm hover:text-yellow-300 transition-colors duration-300 border border-white/30 px-3 py-1 rounded-md hover:border-yellow-300"
            >
              Back to Home
            </Link>
          </div>
        )}

        <div className='flex gap-4 items-center'>
          {user ? (
            <>
              {!isAdminPage && <CartIcon />}
              {!isAdminPage && <BookingModal />}
              {!isAdminPage && (
                <Link
                  to={'/orders'}
                  className={`text-white text-sm hover:text-yellow-300 transition-colors duration-300 ${
                    location.pathname === '/orders' ? 'text-yellow-300' : ''
                  }`}
                >
                  My Orders
                </Link>
              )}
              <div className="flex items-center gap-2">
                <FaUserCircle className="text-white text-lg" />
                <span className="text-white text-sm">{user.name || user.email}</span>
              </div>

              {/* {!isAdminPage && (
                <Link
                  to={'/admin'}
                  className="text-white text-sm hover:text-yellow-300 transition-colors duration-300"
                >
                  Admin
                </Link>
              )} */}

              <button
                onClick={handleLogout}
                className="flex items-center gap-1 text-white text-sm hover:text-gray-200"
              >
                <FaSignOutAlt className="text-white" />
                Logout
              </button>
            </>
          ) : (
            <>
              <Link
                to={'/login'}
                className={`nav-link text-sm relative px-1 py-2 group ${
                  location.pathname === '/login'
                    ? 'text-yellow-300 font-medium'
                    : 'text-white hover:text-white/90'
                }`}
              >
                Login
                <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300 ${
                  location.pathname === '/login'
                    ? 'scale-x-100'
                    : 'scale-x-0 group-hover:scale-x-100'
                }`}></span>
              </Link>
              <Link
                to={'/signup'}
                className="nav-link bg-white text-[#575CEE] text-sm px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-300"
              >
                Sign Up
              </Link>
            </>
          )}

        </div>
      </section>
    </>
  )
}

export default HeaderNav
