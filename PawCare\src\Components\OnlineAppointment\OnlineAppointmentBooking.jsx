import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  FaUser,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaPaw,
  FaCalendarAlt,
  FaClock,
  FaStethoscope,
  FaNotesMedical,
  FaArrowLeft,
  FaCheck,
  FaDog,
  FaCat,
  FaWeight,
  FaBirthdayCake
} from 'react-icons/fa';

const OnlineAppointmentBooking = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Customer Information
    customerName: '',
    email: '',
    phone: '',
    address: '',
    emergencyContact: '',

    // Pet Information
    petName: '',
    petType: '',
    breed: '',
    age: '',
    weight: '',
    gender: '',

    // Appointment Details
    appointmentDate: '',
    appointmentTime: '',
    serviceType: '',
    preferredVet: '',

    // Additional Information
    symptoms: '',
    previousVisits: '',
    medications: '',
    specialRequirements: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const serviceTypes = [
    'General Checkup',
    'Vaccination',
    'Emergency Care',
    'Dental Care',
    'Surgery Consultation',
    'Grooming',
    'Behavioral Consultation',
    'Nutrition Consultation',
    'Senior Pet Care',
    'Puppy/Kitten Care'
  ];

  const timeSlots = [
    '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '12:00 PM', '12:30 PM', '02:00 PM', '02:30 PM', '03:00 PM', '03:30 PM',
    '04:00 PM', '04:30 PM', '05:00 PM', '05:30 PM', '06:00 PM'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};

    if (step === 1) {
      // Customer Information Validation
      if (!formData.customerName.trim()) newErrors.customerName = 'Name is required';
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
      if (!formData.phone.trim()) {
        newErrors.phone = 'Phone number is required';
      } else if (!/^[\d\s\-\(\)\+]+$/.test(formData.phone) || formData.phone.length < 10) {
        newErrors.phone = 'Please enter a valid phone number';
      }
      if (!formData.address.trim()) newErrors.address = 'Address is required';
    }

    if (step === 2) {
      // Pet Information Validation
      if (!formData.petName.trim()) newErrors.petName = 'Pet name is required';
      if (!formData.petType) newErrors.petType = 'Pet type is required';
      if (!formData.age.trim()) newErrors.age = 'Pet age is required';
      if (!formData.gender) newErrors.gender = 'Pet gender is required';
    }

    if (step === 3) {
      // Appointment Details Validation
      if (!formData.appointmentDate) newErrors.appointmentDate = 'Appointment date is required';
      if (!formData.appointmentTime) newErrors.appointmentTime = 'Appointment time is required';
      if (!formData.serviceType) newErrors.serviceType = 'Service type is required';

      // Validate date is not in the past
      const selectedDate = new Date(formData.appointmentDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.appointmentDate = 'Please select a future date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateStep(3)) return;

    setIsSubmitting(true);

    try {
      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to book an appointment.');
        navigate('/login');
        return;
      }

      // Prepare API request
      const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v0';

      const response = await axios.post(`${apiUrl}/appointments/create`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        const appointment = response.data.appointment;

        // Show success message with appointment details
        alert(`🎉 Appointment booked successfully!

📋 Appointment Details:
• Appointment Number: ${appointment.appointmentNumber}
• Patient: ${formData.petName} (${formData.petType})
• Owner: ${formData.customerName}
• Date: ${new Date(formData.appointmentDate).toLocaleDateString()}
• Time: ${formData.appointmentTime}
• Service: ${formData.serviceType}
• Status: ${appointment.status}

📧 A confirmation email will be sent to: ${formData.email}

Thank you for choosing PawCare! Our team will contact you within 24 hours to confirm your appointment.`);

        // Navigate back to home
        navigate('/home');
      } else {
        throw new Error(response.data.message || 'Failed to book appointment');
      }

    } catch (error) {
      console.error('Error booking appointment:', error);

      let errorMessage = 'There was an error booking your appointment. Please try again.';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 401) {
        errorMessage = 'Your session has expired. Please log in again.';
        navigate('/login');
        return;
      } else if (error.response?.status === 409) {
        errorMessage = 'This time slot is already booked. Please choose a different time.';
      }

      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3, 4].map((step) => (
        <React.Fragment key={step}>
          <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
            currentStep >= step
              ? 'bg-[#575CEE] border-[#575CEE] text-white'
              : 'border-gray-300 text-gray-400'
          }`}>
            {currentStep > step ? <FaCheck /> : step}
          </div>
          {step < 4 && (
            <div className={`w-16 h-0.5 ${
              currentStep > step ? 'bg-[#575CEE]' : 'bg-gray-300'
            }`} />
          )}
        </React.Fragment>
      ))}
    </div>
  );

  const renderPetInfo = () => (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
        <FaPaw className="mr-3 text-[#575CEE]" />
        Pet Information
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Pet Name *
          </label>
          <input
            type="text"
            name="petName"
            value={formData.petName}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.petName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your pet's name"
          />
          {errors.petName && <p className="text-red-500 text-sm mt-1">{errors.petName}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Pet Type *
          </label>
          <select
            name="petType"
            value={formData.petType}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.petType ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select pet type</option>
            <option value="Dog">Dog</option>
            <option value="Cat">Cat</option>
            <option value="Bird">Bird</option>
            <option value="Rabbit">Rabbit</option>
            <option value="Hamster">Hamster</option>
            <option value="Fish">Fish</option>
            <option value="Other">Other</option>
          </select>
          {errors.petType && <p className="text-red-500 text-sm mt-1">{errors.petType}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Breed
          </label>
          <input
            type="text"
            name="breed"
            value={formData.breed}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            placeholder="Enter breed (if known)"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Age *
          </label>
          <div className="relative">
            <FaBirthdayCake className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              name="age"
              value={formData.age}
              onChange={handleInputChange}
              className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
                errors.age ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="e.g., 2 years, 6 months"
            />
          </div>
          {errors.age && <p className="text-red-500 text-sm mt-1">{errors.age}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Weight
          </label>
          <div className="relative">
            <FaWeight className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              name="weight"
              value={formData.weight}
              onChange={handleInputChange}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
              placeholder="e.g., 15 kg, 3.5 lbs"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Gender *
          </label>
          <select
            name="gender"
            value={formData.gender}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.gender ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select gender</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
          </select>
          {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
        </div>
      </div>
    </div>
  );

  const renderAppointmentDetails = () => (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
        <FaCalendarAlt className="mr-3 text-[#575CEE]" />
        Appointment Details
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Date *
          </label>
          <input
            type="date"
            name="appointmentDate"
            value={formData.appointmentDate}
            onChange={handleInputChange}
            min={new Date().toISOString().split('T')[0]}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.appointmentDate ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.appointmentDate && <p className="text-red-500 text-sm mt-1">{errors.appointmentDate}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Time *
          </label>
          <select
            name="appointmentTime"
            value={formData.appointmentTime}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.appointmentTime ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select time</option>
            {timeSlots.map(time => (
              <option key={time} value={time}>{time}</option>
            ))}
          </select>
          {errors.appointmentTime && <p className="text-red-500 text-sm mt-1">{errors.appointmentTime}</p>}
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Service Type *
          </label>
          <select
            name="serviceType"
            value={formData.serviceType}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.serviceType ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select service type</option>
            {serviceTypes.map(service => (
              <option key={service} value={service}>{service}</option>
            ))}
          </select>
          {errors.serviceType && <p className="text-red-500 text-sm mt-1">{errors.serviceType}</p>}
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Veterinarian
          </label>
          <select
            name="preferredVet"
            value={formData.preferredVet}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
          >
            <option value="">No preference</option>
            <option value="Dr. Sarah Johnson">Dr. Sarah Johnson</option>
            <option value="Dr. Michael Chen">Dr. Michael Chen</option>
            <option value="Dr. Emily Rodriguez">Dr. Emily Rodriguez</option>
            <option value="Dr. David Thompson">Dr. David Thompson</option>
          </select>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Symptoms or Concerns
          </label>
          <textarea
            name="symptoms"
            value={formData.symptoms}
            onChange={handleInputChange}
            rows="3"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            placeholder="Describe any symptoms or concerns you have about your pet"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Medications
          </label>
          <textarea
            name="medications"
            value={formData.medications}
            onChange={handleInputChange}
            rows="2"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            placeholder="List any medications your pet is currently taking"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Special Requirements or Notes
          </label>
          <textarea
            name="specialRequirements"
            value={formData.specialRequirements}
            onChange={handleInputChange}
            rows="3"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            placeholder="Any special requirements, behavioral notes, or additional information"
          />
        </div>
      </div>
    </div>
  );

  const renderReviewAndConfirm = () => (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
        <FaCheck className="mr-3 text-[#575CEE]" />
        Review & Confirm
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Customer Information Summary */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h4 className="font-semibold text-lg mb-4 flex items-center">
            <FaUser className="mr-2 text-[#575CEE]" />
            Customer Information
          </h4>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">Name:</span> {formData.customerName}</p>
            <p><span className="font-medium">Email:</span> {formData.email}</p>
            <p><span className="font-medium">Phone:</span> {formData.phone}</p>
            <p><span className="font-medium">Address:</span> {formData.address}</p>
            {formData.emergencyContact && (
              <p><span className="font-medium">Emergency Contact:</span> {formData.emergencyContact}</p>
            )}
          </div>
        </div>

        {/* Pet Information Summary */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h4 className="font-semibold text-lg mb-4 flex items-center">
            <FaPaw className="mr-2 text-[#575CEE]" />
            Pet Information
          </h4>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">Name:</span> {formData.petName}</p>
            <p><span className="font-medium">Type:</span> {formData.petType}</p>
            {formData.breed && <p><span className="font-medium">Breed:</span> {formData.breed}</p>}
            <p><span className="font-medium">Age:</span> {formData.age}</p>
            {formData.weight && <p><span className="font-medium">Weight:</span> {formData.weight}</p>}
            <p><span className="font-medium">Gender:</span> {formData.gender}</p>
          </div>
        </div>

        {/* Appointment Details Summary */}
        <div className="bg-gray-50 p-6 rounded-lg md:col-span-2">
          <h4 className="font-semibold text-lg mb-4 flex items-center">
            <FaCalendarAlt className="mr-2 text-[#575CEE]" />
            Appointment Details
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <p><span className="font-medium">Date:</span> {new Date(formData.appointmentDate).toLocaleDateString()}</p>
            <p><span className="font-medium">Time:</span> {formData.appointmentTime}</p>
            <p><span className="font-medium">Service:</span> {formData.serviceType}</p>
            {formData.preferredVet && <p><span className="font-medium">Veterinarian:</span> {formData.preferredVet}</p>}
          </div>

          {(formData.symptoms || formData.medications || formData.specialRequirements) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              {formData.symptoms && (
                <p className="mb-2"><span className="font-medium">Symptoms:</span> {formData.symptoms}</p>
              )}
              {formData.medications && (
                <p className="mb-2"><span className="font-medium">Medications:</span> {formData.medications}</p>
              )}
              {formData.specialRequirements && (
                <p><span className="font-medium">Special Requirements:</span> {formData.specialRequirements}</p>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-blue-800 text-sm">
          <strong>Please note:</strong> This is a booking request. Our team will contact you within 24 hours to confirm your appointment and provide any additional instructions.
        </p>
      </div>
    </div>
  );

  const renderCustomerInfo = () => (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
        <FaUser className="mr-3 text-[#575CEE]" />
        Customer Information
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Name *
          </label>
          <input
            type="text"
            name="customerName"
            value={formData.customerName}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.customerName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your full name"
          />
          {errors.customerName && <p className="text-red-500 text-sm mt-1">{errors.customerName}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </label>
          <div className="relative">
            <FaEnvelope className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
            />
          </div>
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number *
          </label>
          <div className="relative">
            <FaPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
                errors.phone ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="+92 300 1234567"
            />
          </div>
          {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Emergency Contact
          </label>
          <input
            type="tel"
            name="emergencyContact"
            value={formData.emergencyContact}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            placeholder="Emergency contact number"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Address *
        </label>
        <div className="relative">
          <FaMapMarkerAlt className="absolute left-3 top-4 text-gray-400" />
          <textarea
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            rows="3"
            className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-[#575CEE] focus:border-transparent ${
              errors.address ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your complete address"
          />
        </div>
        {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/home')}
                className="mr-4 p-2 text-gray-600 hover:text-[#575CEE] transition-colors duration-200"
              >
                <FaArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 flex items-center">
                  <FaStethoscope className="mr-3 text-[#575CEE]" />
                  Online Appointment Booking
                </h1>
                <p className="text-gray-600 mt-1">Schedule your pet's appointment with our expert veterinarians</p>
              </div>
            </div>
          </div>
        </div>

        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Form Content */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <form onSubmit={handleSubmit}>
            {currentStep === 1 && renderCustomerInfo()}
            {currentStep === 2 && renderPetInfo()}
            {currentStep === 3 && renderAppointmentDetails()}
            {currentStep === 4 && renderReviewAndConfirm()}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => navigate('/home')}
                className="px-6 py-3 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Cancel
              </button>

              <div className="flex space-x-4">
                {currentStep > 1 && (
                  <button
                    type="button"
                    onClick={handlePrevious}
                    className="px-6 py-3 text-[#575CEE] border border-[#575CEE] rounded-lg hover:bg-[#575CEE] hover:text-white transition-colors duration-200"
                  >
                    Previous
                  </button>
                )}

                {currentStep < 4 ? (
                  <button
                    type="button"
                    onClick={handleNext}
                    className="px-6 py-3 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1] transition-colors duration-200"
                  >
                    Next Step
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Booking...' : 'Book Appointment'}
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OnlineAppointmentBooking;
