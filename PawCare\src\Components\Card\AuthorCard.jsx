import React from 'react';
import { useNavigate } from 'react-router-dom';

const AuthorCard = ({
  id,
  authorName = "Manu Arora",
  readTime = "2 min read",
  title = "Author Card",
  description = "Card with Author avatar, complete name and time to read - most suitable for blogs.",
  avatarSrc = "https://images.pexels.com/photos/8159657/pexels-photo-8159657.jpeg?auto=compress&cs=tinysrgb&w=600",
  backgroundImage = "https://images.unsplash.com/photo-1544077960-604201fe74bc?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1651&q=80",
  onClick
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (id) {
      // Navigate to the pet guide page with the specific guide ID
      navigate(`/pet-guide/${id}`);
    } else {
      // Navigate to the general pet guide page
      navigate('/pet-guide');
    }
  };

  return (
    <div className="max-w-xs w-full group/card">
      <div
        onClick={handleClick}
        className={`cursor-pointer overflow-hidden relative card h-96 rounded-md shadow-xl max-w-sm mx-auto flex flex-col justify-between p-4 bg-cover transform transition-all duration-300 hover:scale-105 hover:shadow-2xl`}
        style={{ backgroundImage: `url(${backgroundImage})` }}
      >
        <div className="absolute w-full h-full top-0 left-0 transition duration-300 group-hover/card:bg-black opacity-60 group-hover/card:opacity-75"></div>

        <div className="flex flex-row items-center space-x-4 z-10">
          <img
            height="100"
            width="100"
            alt={`${authorName}'s avatar`}
            src={avatarSrc}
            className="h-10 w-10 rounded-full border-2 object-cover"
          />
          <div className="flex flex-col">
            <p className="font-normal text-base text-gray-50 relative z-10">
              {authorName}
            </p>
            <p className="text-sm text-gray-400">{readTime}</p>
          </div>
        </div>

        <div className="text content">
          <h1 className="font-bold text-xl md:text-2xl text-gray-50 relative z-10 group-hover/card:text-white transition-colors duration-300">
            {title}
          </h1>
          <p className="font-normal text-sm text-gray-50 relative z-10 my-4 group-hover/card:text-gray-200 transition-colors duration-300">
            {description}
          </p>

          {/* Read More Button */}
          <div className="relative z-10 mt-4">
            <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium group-hover/card:bg-white/30 transition-all duration-300">
              Read Guide →
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthorCard;