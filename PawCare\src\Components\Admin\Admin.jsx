import React, { useState, useEffect } from 'react';
import { Chart } from 'chart.js/auto';
import { Bar,  Doughnut , } from 'react-chartjs-2';
import axios from 'axios';
import UserSection from './User/User';
import ProductsSection from './Products/ProductsSection';
import OrdersSection from './Orders/OrdersSection';
import AppointmentsSection from './Appointments/AppointmentsSection';
import Settings from './Settings/Settings';
import {
  FaChartPie,
  FaUsers,
  FaShoppingCart,
  FaCalendarAlt,
  FaCog,
  FaBell,
  FaSearch,
  FaSignOutAlt,
  FaTachometerAlt,
  FaBoxOpen,
  FaMoneyBillWave,
  FaArrowLeft
} from 'react-icons/fa';

const SystemAdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [notifications] = useState(0);
  const [recentUsers, setRecentUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [products, setProducts] = useState([]);

  const fetchProducts = async () => {
    try {
      console.log('Fetching products...');
      const response = await axios.get('http://localhost:3000/api/v0/product/all');
      console.log('Product API response:', response.data);

      // Check if response.data has a nested data property (common API pattern)
      let productsData;
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        productsData = response.data.data;
      } else if (response.data && response.data.products && Array.isArray(response.data.products)) {
        productsData = response.data.products;
      } else if (Array.isArray(response.data)) {
        productsData = response.data;
      } else if (response.data && typeof response.data === 'object') {
        // Look for any array property in the response
        const arrayProp = Object.keys(response.data).find(key => Array.isArray(response.data[key]));
        if (arrayProp) {
          productsData = response.data[arrayProp];
        } else {
          // If we can't find an array, use an empty array
          productsData = [];
        }
      } else {
        productsData = [];
      }

      console.log('Processed products data:', productsData);
      setProducts(productsData);
    } catch (err) {
      console.error('Error fetching products:', err);
      setProducts([]); // Set empty array on error
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);




  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await fetch('http://localhost:3000/api/v0/data/alluser');
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const responseData = await response.json();
        // Check the structure of the response and extract the users array
        console.log('API Response:', responseData); // Debug log to see the structure

        // Handle different possible response structures
        let usersArray = [];
        if (Array.isArray(responseData)) {
          usersArray = responseData;
        } else if (responseData && typeof responseData === 'object') {
          // Check if the response has a data property or users property
          if (Array.isArray(responseData.data)) {
            usersArray = responseData.data;
          } else if (Array.isArray(responseData.users)) {
            usersArray = responseData.users;
          } else if (responseData.user && Array.isArray(responseData.user)) {
            usersArray = responseData.user;
          } else {
            // If we can't find an array in standard places, try to find any array property
            const arrayProps = Object.keys(responseData).find(key => Array.isArray(responseData[key]));
            if (arrayProps) {
              usersArray = responseData[arrayProps];
            } else {
              // If we still don't have an array, just use the object itself in an array
              usersArray = [responseData];
            }
          }
        }

        // Sort the users by creation date (if it's an array)
        if (usersArray.length > 0) {
          const sortedUsers = usersArray.sort((a, b) => {
            return new Date(b.createdAt || b.joinedDate || Date.now()) -
                   new Date(a.createdAt || a.joinedDate || Date.now());
          }).slice(0, 4);
          setRecentUsers(sortedUsers);
        } else {
          setRecentUsers([]);
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Failed to load users. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Dynamic stats based on actual data
  const stats = [
    {
      id: 1,
      title: 'Total Users',
      value: recentUsers.length.toString(),
      icon: <FaUsers />,
      change: '+12%',
      color: 'bg-blue-500'
    },
    {
      id: 2,
      title: 'Total Products',
      value: products.length.toString(),
      icon: <FaBoxOpen />,
      change: '+5%',
      color: 'bg-green-500'
    },
    {
      id: 3,
      title: 'Total Orders',
      value: '843',
      icon: <FaShoppingCart />,
      change: '+18%',
      color: 'bg-purple-500'
    },
    {
      id: 4,
      title: 'Revenue',
      value: '$24,500',
      icon: <FaMoneyBillWave />,
      change: '+8%',
      color: 'bg-yellow-500'
    },
  ];



  // Navigation items
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: <FaTachometerAlt /> },
    { id: 'users', label: 'Users', icon: <FaUsers /> },
    { id: 'products', label: 'Products', icon: <FaBoxOpen /> },
    { id: 'orders', label: 'Orders', icon: <FaShoppingCart /> },
    { id: 'appointments', label: 'Appointments', icon: <FaCalendarAlt /> },
    { id: 'settings', label: 'Settings', icon: <FaCog /> }
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className={`bg-white w-64 shadow-md transition-all duration-300 ${sidebarOpen ? 'translate-x-0' : '-translate-x-64'}`}>
        <div className="p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-800">Admin Panel</h1>
            <button onClick={() => setSidebarOpen(false)} className="lg:hidden">
              <FaArrowLeft />
            </button>
          </div>
        </div>
        <nav className="mt-4">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`w-full flex items-center px-4 py-3 text-left transition-colors
                ${activeTab === item.id
                  ? 'bg-[#575CEE] text-white'
                  : 'text-gray-600 hover:bg-gray-50'}`}
            >
              <span className="text-xl mr-3">{item.icon}</span>
              <span className="text-sm font-medium">{item.label}</span>
            </button>
          ))}
        </nav>
      </aside>

      {/* Main content wrapper */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center">
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <FaSearch className="text-gray-400" />
                </span>
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <button className="text-gray-500 hover:text-gray-700 focus:outline-none">
                  <FaBell className="text-xl" />
                  {notifications > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">
                      {notifications}
                    </span>
                  )}
                </button>
              </div>
              <div className="flex items-center">
                <img
                  src="https://randomuser.me/api/portraits/men/1.jpg"
                  alt="Admin"
                  className="w-8 h-8 rounded-full"
                />
                <div className="ml-2">
                  <p className="text-sm font-medium text-gray-700">Admin User</p>
                  <p className="text-xs text-gray-500">System Administrator</p>
                </div>
              </div>
              <button className="text-gray-500 hover:text-gray-700 focus:outline-none">
                <FaSignOutAlt className="text-xl" />
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4">
          {activeTab === 'dashboard' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-semibold text-gray-800">Dashboard</h1>
                <div>
                  <button className="bg-[#575CEE] text-white px-4 py-2 rounded-lg hover:bg-[#4a4fd1] transition-colors">
                    Generate Report
                  </button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <button
                    onClick={() => {
                      setActiveTab('products');
                    }}
                    className="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors"
                  >
                    <FaBoxOpen className="text-blue-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Add Product</span>
                  </button>
                  <button className="bg-green-50 hover:bg-green-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaUsers className="text-green-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Add User</span>
                  </button>
                  <button className="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaCalendarAlt className="text-purple-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Schedule</span>
                  </button>
                  <button className="bg-yellow-50 hover:bg-yellow-100 p-4 rounded-lg flex flex-col items-center justify-center transition-colors">
                    <FaChartPie className="text-yellow-500 text-2xl mb-2" />
                    <span className="text-sm font-medium text-gray-700">Reports</span>
                  </button>
                </div>
              </div>

              {/* Stats Cards */}

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {stats.map((stat) => (
                  <div key={stat.id} className="bg-white rounded-lg shadow-sm p-6 flex items-start">
                    <div className={`${stat.color} text-white p-3 rounded-lg`}>
                      {stat.icon}
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-500">{stat.title}</p>
                      <h3 className="text-xl font-semibold text-gray-800">{stat.value}</h3>
                      <p className="text-xs text-green-500">{stat.change} from last month</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-800">Revenue Overview</h2>
                    <select className="text-sm border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#575CEE]">
                      <option>Last 7 Days</option>
                      <option>Last 30 Days</option>
                      <option>Last 90 Days</option>
                    </select>
                  </div>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    <Bar
                    data={{
                      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                      datasets: [
                        {
                          label: 'Revenue',
                          data: [65, 59, 80, 81, 56, 55, 40],
                          backgroundColor: [
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(255, 159, 64, 0.2)',
                            'rgba(255, 205, 86, 0.2)',
                            'rgba(75, 192, 192, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(153, 102, 255, 0.2)',
                            'rgba(201, 203, 207, 0.2)'
                          ],
                          borderColor: [
                            'rgb(255, 99, 132)',
                            'rgb(255, 159, 64)',
                            'rgb(255, 205, 86)',
                            'rgb(75, 192, 192)',
                            'rgb(54, 162, 235)',
                            'rgb(153, 102, 255)',
                            'rgb(201, 203, 207)'
                          ],
                          borderWidth: 1,
                        },
                      ],
                      options: {
                        scales: {
                          y: {
                            beginAtZero: true,
                          },
                        },
                      },
                    }}

                   />
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-800">Sales Analytics</h2>
                    <select className="text-sm border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#575CEE]">
                      <option>Last 7 Days</option>
                      <option>Last 30 Days</option>
                      <option>Last 90 Days</option>
                    </select>
                  </div>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">

                    <Doughnut
                    data={{
                      labels: [
                        'OverAll Sales',
                        'Sales',
                        'Cancle Orders'
                      ],
                      datasets: [{
                        label: 'My First Dataset',
                        data: [300, 50, 100],
                        backgroundColor: [
                          'rgb(255, 99, 132)',
                          'rgb(54, 162, 235)',
                          'rgb(255, 205, 86)'
                        ],
                        hoverOffset: 4
                      }]
                    }}
                  />
                  </div>
                </div>
              </div>

              {/* Recent Users */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-800">Recent Users</h2>
                  <button className="text-[#575CEE] hover:underline text-sm focus:outline-none">
                    View All
                  </button>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Joined
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {loading ? (
                        <tr>
                          <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                            Loading users...
                          </td>
                        </tr>
                      ) : error ? (
                        <tr>
                          <td colSpan="5" className="px-6 py-4 text-center text-red-500">
                            {error}
                          </td>
                        </tr>
                      ) : recentUsers.length === 0 ? (
                        <tr>
                          <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                            No users found
                          </td>
                        </tr>
                      ) : (
                        recentUsers.map((user) => (
                          <tr key={user._id || user.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                  {(user.name || user.username || '').charAt(0)}
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">{user.name || user.username}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.email}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                                {user.role || user.type || 'User'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <button className="text-[#575CEE] hover:underline mr-3">Edit</button>
                              <button className="text-red-500 hover:underline">Delete</button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab !== 'dashboard' && activeTab === 'users' && (
            <UserSection />
          )}

          {activeTab === 'products' && (
            <ProductsSection />
          )}

          {activeTab === 'orders' && (
            <OrdersSection />
          )}

          {activeTab === 'appointments' && (
            <AppointmentsSection />
          )}

          {activeTab === 'settings' && (
            <Settings />
          )}
        </main>
      </div>
    </div>
  );
};

export default SystemAdminDashboard;