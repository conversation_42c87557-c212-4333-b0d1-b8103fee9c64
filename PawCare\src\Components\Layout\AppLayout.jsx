import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import HeaderNav from '../Header/Header.jsx';
import FooterComponent from '../Footer/Footer.jsx';

const AppLayout = ({ children }) => {
  const location = useLocation();
  const pathname = location.pathname;
  const isAuthPage = pathname === '/login' || pathname === '/signup' || pathname === '/';
  const [forceUpdate, setForceUpdate] = useState(0);

  console.log('🔍 AppLayout: Current path', { pathname, isAuthPage });

  // Listen for authentication state changes to force re-render
  useEffect(() => {
    const handleAuthChange = () => {
      console.log('🔄 AppLayout: Auth state change detected, forcing re-render');
      setForceUpdate(prev => prev + 1);
    };

    window.addEventListener('authStateChanged', handleAuthChange);
    return () => window.removeEventListener('authStateChanged', handleAuthChange);
  }, []);

  return (
    <div className="w-full flex flex-col min-h-screen" key={`layout-${forceUpdate}`}>
      {!isAuthPage && (
        <nav className='pt-5 pr-10 bg-[#575CEE] h-20'>
          <HeaderNav />
        </nav>
      )}

      <main className={`flex-grow ${isAuthPage ? 'flex items-center justify-center' : ''}`}>
        <div key={`content-${forceUpdate}-${pathname}`}>
          {children}
        </div>
      </main>

      {!isAuthPage && (
        <footer className='mt-5 w-full'>
          <FooterComponent />
        </footer>
      )}
    </div>
  );
};

export default AppLayout;
