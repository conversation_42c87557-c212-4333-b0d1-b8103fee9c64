require('dotenv').config();
const exp = require('express');
const app = exp();
const cors = require('cors')
const ConnectDb = require('./config/Db');
const  Route = require('./route/rawDataRoute');
const paymentRoute = require('./route/paymentRoute');
const productRoute = require('./route/productRoutes');
const orderRoute = require('./route/orderRoutes');
const appointmentRoute = require('./route/appointmentRoutes');
const Order = require('./model/orderModel');
const path = require('path');

// app.get('/', (req, res) => {
//     res.send('Hello World!');
// });

app.use(cors());
app.use(exp.json());
app.use('/api/v0/data',Route);
app.use('/api/v0/payment', paymentRoute);
app.use('/api/v0/product', productRoute);
app.use('/api/v0/orders', orderRoute);
app.use('/api/v0/appointments', appointmentRoute);
app.use('/images', exp.static(path.join(__dirname, 'tmp/images')));

// Database initialization and index cleanup
const initializeDatabase = async () => {
    try {
        // Connect to database
        await ConnectDb();

        // Check for and handle duplicate indexes
        console.log('🔧 Checking database indexes...');

        // Suppress mongoose index warnings during cleanup
        const originalWarn = console.warn;
        console.warn = (message) => {
            if (!message.includes('Duplicate schema index')) {
                originalWarn(message);
            }
        };

        // Ensure indexes are properly set up
        await Order.ensureIndexes();

        // Restore console.warn
        console.warn = originalWarn;

        console.log('✅ Database indexes verified');

    } catch (error) {
        console.error('❌ Database initialization error:', error);
        // Don't exit the process, just log the error
    }
};

app.listen(3000, async () => {
    console.log('🚀 Server starting on port 3000...');
    await initializeDatabase();
    console.log('✅ Server started successfully');
});