// Authentication utility functions
export const getAuthToken = () => {
  return localStorage.getItem('token');
};

export const getUserId = () => {
  return localStorage.getItem('userId');
};

export const isAuthenticated = () => {
  const token = getAuthToken();
  const authFlag = localStorage.getItem('isAuthenticated');
  return !!(token && authFlag);
};

export const getAuthHeaders = () => {
  const token = getAuthToken();
  if (!token) {
    console.warn('No authentication token found');
    return {};
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

export const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('userId');
  localStorage.removeItem('userName');
  localStorage.removeItem('userEmail');
};

export const debugAuthState = () => {
  const token = getAuthToken();
  const authData = {
    token: token,
    tokenLength: token?.length,
    tokenStart: token?.substring(0, 30) + '...',
    tokenEnd: '...' + token?.substring(token?.length - 30),
    userId: getUserId(),
    isAuthenticated: isAuthenticated(),
    userName: localStorage.getItem('userName'),
    userEmail: localStorage.getItem('userEmail'),
    authFlag: localStorage.getItem('isAuthenticated')
  };

  console.log('🔐 Frontend Auth Debug State:', authData);

  // Try to decode the token
  if (token) {
    try {
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        const now = Math.floor(Date.now() / 1000);
        console.log('🔍 Token Payload:', {
          id: payload.id,
          email: payload.email,
          name: payload.name,
          exp: payload.exp,
          iat: payload.iat,
          isExpired: payload.exp < now,
          timeUntilExpiry: payload.exp - now,
          expiryDate: new Date(payload.exp * 1000).toISOString()
        });
      }
    } catch (error) {
      console.error('❌ Failed to decode token:', error);
    }
  }

  return authData;
};
